import { useEffect } from 'react';
import { useMultiFormGuardContext } from './useMultiFormGuard';

type UseMultiFormGuardFormProps = {
  id: string;
  isDirty: boolean;
  save: () => Promise<void>;
};

export const useMultiFormGuardForm = ({ id, isDirty, save }: UseMultiFormGuardFormProps) => {
  const { registerForm, unregisterForm, setFormDirty } = useMultiFormGuardContext();

  // Register the form with its latest save/reset handlers
  useEffect(() => {
    registerForm(id, save, isDirty);
    return () => unregisterForm(id);
  }, [id, save]);

  // Keep dirty state updated in context
  useEffect(() => {
    setFormDirty(id, isDirty);
  }, [id, isDirty, setFormDirty]);
};
