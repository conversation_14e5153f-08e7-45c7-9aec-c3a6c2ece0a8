import { Checkbox } from '@/components/ui/Checkbox';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { ColumnDef } from '@tanstack/react-table';
import { AdminAssociation } from '@/generated/graphql';
import { format } from 'date-fns';
import { DATE_FORMAT } from '@/lib/constants';

interface AssociationColumnsProps {
  isLoading: boolean;
  onNavigate: (associationId: string) => void;
}

export function generateAssociationColumns({ isLoading, onNavigate }: AssociationColumnsProps) {
  const columns: ColumnDef<AdminAssociation>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      size: 50,
    },
    {
      accessorKey: 'name',
      header: ({ column }) => <HeaderColumn column={column}>HOA Association</HeaderColumn>,
      cell: ({ row }) => {
        const association = row.original;
        const associationName = association?.name || 'Unnamed Association';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full justify-start items-start'>
              <span
                className='truncate text-gray-900 font-medium cursor-pointer hover:text-primary hover:underline'
                onClick={(e) => {
                  e.stopPropagation();
                  onNavigate(association.id);
                }}
              >
                {associationName}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'memberCount',
      header: ({ column }) => <HeaderColumn column={column}>Total Users</HeaderColumn>,
      cell: ({ row }) => {
        const memberCount = row.original.memberCount;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>
              {memberCount || 0}
            </span>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'clubMemberCount',
      header: ({ column }) => <HeaderColumn column={column}>Active Clubs</HeaderColumn>,
      cell: ({ row }) => {
        const clubMemberCount = row.original.activeClubCount;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>
              {clubMemberCount || 0}
            </span>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
  ];
  return columns;
}
