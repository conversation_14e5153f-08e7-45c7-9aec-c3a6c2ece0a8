import { Button } from '@/components/ui/Button';
import { Form } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { Label } from '@/components/ui/form/Label';
import { Separator } from '@/components/ui/Separator';
import { Skeleton } from '@/components/ui/Skeleton';
import {
  UpdatePasswordInput,
  useMeQuery,
  useUpdatePasswordMutation,
  useUpdateProfileMutation,
} from '@/generated/graphql';
import { useMultiFormGuardForm } from '@/hooks/useMultiFormGuardForm';
import { useToast } from '@/hooks/useToast';
import { emailSchema } from '@/lib/schemas';
import { useAuthContext } from '@/pages/auth/AuthContext';
import { ApolloError } from '@apollo/client';
import { zodResolver } from '@hookform/resolvers/zod';
import { Mail } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import ResetPasswordModal from './components/reset-password-modal/ResetPasswordModal';

// Schema for validation using Zod
const formSchema = z.object({
  firstName: z
    .string()
    .min(1, 'First name is required')
    .transform((val) => val.trim()),
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .transform((val) => val.trim()),
  email: emailSchema,
});

type FormValues = z.infer<typeof formSchema>;

const ProfileInfo = () => {
  const { data, loading, refetch } = useMeQuery();
  const [updateProfile, { loading: updateProfileLoading }] = useUpdateProfileMutation();
  const [resetPassword, { loading: resetPasswordLoading }] = useUpdatePasswordMutation();
  const { refreshUserData } = useAuthContext();

  const [isOpenResetPassword, setIsOpenResetPassword] = useState(false);
  const { toast } = useToast();

  // Default values memoized from user data
  const defaultValues: FormValues = useMemo(
    () => ({
      firstName: data?.me?.firstName ?? '',
      lastName: data?.me?.lastName ?? '',
      email: data?.me?.email ?? '',
    }),
    [data]
  );

  // Create RHF form instance manually
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
    mode: 'onChange',
  });

  useEffect(() => {
    if (data?.me) {
      form.reset({
        firstName: data.me.firstName ?? '',
        lastName: data.me.lastName ?? '',
        email: data.me.email ?? '',
      });
    }
  }, [data?.me]);

  // Register form with form guard system
  const isDirty = form.formState.isDirty;
  useMultiFormGuardForm({
    id: 'profileInfo',
    isDirty: isDirty,
    save: async () => {
      const values = form.getValues();
      await handleSubmit(values);
    },
  });

  // Submit handler
  const handleSubmit = async (values: FormValues) => {
    if (!form.formState.isValid) return;
    try {
      await updateProfile({ variables: { input: values } });
      toast({
        title: 'Your profile has been updated',
        variant: 'success',
      });
      form.reset(values);
      await refetch();
      await refreshUserData();
    } catch {
      toast({
        title: 'Failed to update profile',
        variant: 'destructive',
      });
    }
  };

  const handleReset = useCallback(() => {
    setIsOpenResetPassword(true);
  }, []);

  const onResetPassword = useCallback(
    (formValue: UpdatePasswordInput) => {
      resetPassword({ variables: { input: formValue } })
        .then((rs) => {
          toast({
            title: rs.data?.updatePassword?.message ?? '',
            variant: 'success',
          });
          setIsOpenResetPassword(false);
        })
        .catch((err) => {
          if (err instanceof ApolloError) {
            return toast({
              variant: 'destructive',
              title: err.graphQLErrors[0].message,
            });
          }
        });
    },
    [resetPassword, toast]
  );

  return (
    <div className='rounded-xl border'>
      {/* Header section */}
      <div className='sm:p-6 p-4 pb-6 flex flex-col gap-5'>
        <div className='flex justify-between items-start'>
          <div>
            <h2 className='text-lg font-semibold text-[#191E3B]'>Profile info</h2>
            <p className='text-sm text-[#52577A] mt-1'>Update your personal details here.</p>
          </div>
          <div className='hidden sm:flex gap-2'>
            {loading ? (
              <Skeleton className='h-11 w-24' />
            ) : (
              <Button
                disabled={!isDirty || !form.formState.isValid}
                type='submit'
                form='profile-form'
                loading={updateProfileLoading}
              >
                Save
              </Button>
            )}
          </div>
        </div>
        <Separator className='bg-[#EBEDF4] hidden sm:block' />
      </div>

      {/* Form section */}
      <div className='sm:px-8 px-4 pb-6'>
        <Form
          id='profile-form'
          forminstance={form} //renamed from form ➝ formInstance
          onSubmit={handleSubmit}
          className='space-y-6'
          showUnsavedChangesConfirmModal
        >
          <div className='flex flex-col sm:flex-row items-start gap-4 sm:gap-8'>
            <div className='w-full sm:w-64'>
              <label className='font-semibold text-sm hidden sm:block'>Name</label>
            </div>
            <div className='flex flex-col sm:flex-row gap-6 sm:gap-6 flex-1 max-w-lg w-full'>
              {loading ? (
                <>
                  <Skeleton className='h-11 w-full sm:flex-1' />
                  <Skeleton className='h-11 w-full sm:flex-1' />
                </>
              ) : (
                <>
                  <div className='flex flex-col w-full sm:flex-1'>
                    <Label className='font-semibold text-sm mb-1 sm:hidden'>First Name</Label>
                    <FormInput
                      name='firstName'
                      className='w-full'
                      placeholder='First Name'
                      disabled={updateProfileLoading}
                    />
                  </div>
                  <div className='flex flex-col w-full sm:flex-1'>
                    <Label className='font-semibold text-sm mb-1 sm:hidden'>Last Name</Label>
                    <FormInput
                      name='lastName'
                      className='w-full'
                      placeholder='Last Name'
                      disabled={updateProfileLoading}
                    />
                  </div>
                </>
              )}
            </div>
          </div>

          <Separator className='hidden sm:block bg-[#EBEDF4]' />

          <div className='flex flex-col sm:flex-row items-start gap-2 sm:gap-8'>
            <div className='w-full sm:w-64'>
              <label className='font-semibold text-sm'>Email Address</label>
            </div>
            <div className='w-full sm:max-w-lg flex-1'>
              {loading ? (
                <Skeleton className='h-11 w-full' />
              ) : (
                <FormInput
                  StartIcon={Mail}
                  name='email'
                  type='email'
                  className='w-full'
                  placeholder='Email Address'
                />
              )}
            </div>
          </div>

          <Separator className='hidden sm:block bg-[#EBEDF4]' />

          <div className='flex items-start justify-between'>
            <div className='flex flex-1 items-start gap-8'></div>
            {loading ? (
              <Skeleton className='h-11 w-32' />
            ) : (
              <Button
                variant='outline'
                type='button'
                onClick={handleReset}
                className='font-semibold text-sm'
              >
                Reset Password
              </Button>
            )}
          </div>

          <Separator className='sm:hidden block bg-[#EBEDF4]' />

          <div className='flex sm:hidden mt-4 justify-end'>
            <Button
              type='submit'
              form='profile-form'
              disableOnInvalid
              disabledOnNotDirty
              loading={updateProfileLoading}
            >
              Save changes
            </Button>
          </div>
        </Form>

        <ResetPasswordModal
          open={isOpenResetPassword}
          onCancel={() => setIsOpenResetPassword(false)}
          onConfirm={onResetPassword}
          isLoading={resetPasswordLoading}
          onOpenChange={setIsOpenResetPassword}
        />
      </div>
    </div>
  );
};

export default ProfileInfo;
