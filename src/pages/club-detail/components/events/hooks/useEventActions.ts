import {
  AdminClubEventsDocument,
  useAdminDeleteClubEventMutation,
  useAdminUnflagReportsByEventIdMutation,
  useToggleUserClubFeatureMutation,
} from '@/generated/graphql';
import { useToast } from '@/hooks/useToast';

import { TOAST_DURATION } from '@/lib/constants';
import { ApolloError } from '@apollo/client';
import { useCallback } from 'react';

export const useEventActions = () => {
  const [removeEvent, { loading: isRemovingEvent }] = useAdminDeleteClubEventMutation();
  const [disableClubAccess, { loading: isDisablingClubAccess }] =
    useToggleUserClubFeatureMutation();
  const [unflagEvent, { loading: isUnflaggingEvent }] = useAdminUnflagReportsByEventIdMutation();

  const { toast } = useToast();

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  const handleRemoveEvent = useCallback(
    async (eventId: string) => {
      try {
        await removeEvent({
          variables: { clubEventId: eventId },
          refetchQueries: [AdminClubEventsDocument],
        });
        toast({
          variant: 'success',
          title: 'Event deleted successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while removing event');
      }
    },
    [handleApiError, removeEvent, toast]
  );

  const handleDisableClubAccess = useCallback(
    async (userId: string) => {
      try {
        await disableClubAccess({ variables: { userId } });
        toast({
          variant: 'success',
          title: 'Club access disabled successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while disabling club access');
      }
    },
    [disableClubAccess, handleApiError, toast]
  );

  const handleUnflagEvent = useCallback(
    async (eventId: string) => {
      try {
        await unflagEvent({
          variables: { eventId },
          refetchQueries: [AdminClubEventsDocument],
        });
        toast({
          variant: 'success',
          title: 'Event unflagged successfully',
          duration: TOAST_DURATION,
        });
      } catch (error) {
        handleApiError(error, 'Error while unflagging event');
      }
    },
    [handleApiError, toast, unflagEvent]
  );

  return {
    isRemovingEvent,
    isDisablingClubAccess,
    isUnflaggingEvent,
    handleRemoveEvent,
    handleDisableClubAccess,
    handleUnflagEvent,
  };
};
