import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { Skeleton } from '@/components/ui/Skeleton';
import { AdminClubEvent } from '@/generated/graphql';
import { Flag, Heart, MoreHorizontal } from 'lucide-react';
import { formatEventDateTime, getDisplayName } from '../../utils';
import { cn } from '@/lib/utils';
import { useMemo, useState } from 'react';

interface EventTableItemProps {
  event: AdminClubEvent;
  isLoading?: boolean;
  showActions: boolean;
  onRemoveEvent: (eventId: string) => void;
  onDisableClubAccess: (userId: string) => void;
  onUnflagEvent: (eventId: string) => void;
}

const EventTableItem = ({
  event,
  isLoading = false,
  showActions,
  onRemoveEvent,
  onDisableClubAccess,
  onUnflagEvent,
}: EventTableItemProps) => {
  const [isShowFullDescription, setIsShowFullDescription] = useState(false);

  const hasReports = event.reports && event.reports.length > 0;
  const displayName = getDisplayName(
    event.clubProfile?.displayName,
    event.clubProfile?.displayName
  );
  const eventDescription = useMemo(() => {
    if (isShowFullDescription) {
      return event.description;
    }
    return event.description?.slice(0, 150);
  }, [isShowFullDescription, event.description]);

  if (isLoading) {
    return <EventTableItemSkeleton />;
  }

  return (
    <div
      key={event.id}
      className={cn('p-4 sm:p-6 border-b', hasReports ? 'bg-[#FCEFED80]' : 'border')}
    >
      {/* Header with title and flag */}
      <div className='flex items-start justify-between mb-2'>
        <div className='flex flex-col sm:flex-row gap-2'>
          <h3 className='text-sm font-medium text-gray-900'>{event.name || 'Event'}</h3>
          {hasReports && (
            <Badge className='bg-[#FCEFED] hover:bg-[#FCEFED] gap-2 font-medium text-sm text-destructive-foreground border-none'>
              <Flag className='w-4 h-4 flex-shrink-0 text-destructive-foreground' />
              {event.reports?.[0]?.category?.title || 'Inappropriate content'}
            </Badge>
          )}
        </div>
        {showActions && (
          <div className='flex items-center gap-2'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                  <MoreHorizontal className='w-6 h-6' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                {hasReports && (
                  <>
                    <DropdownMenuItem
                      onClick={() => {
                        if (event.clubProfile?.id) {
                          onDisableClubAccess(event.clubProfile.id);
                        }
                      }}
                    >
                      Disable Club Access
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUnflagEvent(event.id)}>
                      Unflag Event
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem onClick={() => onRemoveEvent(event.id)}>
                  Delete Event
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Date and time */}
      <p className='text-[13px] text-muted-foreground mb-1'>
        {formatEventDateTime(event.startTime, event.endTime)}
      </p>

      {/* Location */}
      {event.location && <p className='text-[13px] text-muted-foreground mb-4'>{event.location}</p>}

      {/* Description */}
      <div className='text-gray-700 mb-4'>
        <p className='text-sm'>
          {eventDescription}
          {event.description && event.description.length > 150 && (
            <span
              className='text-primary cursor-pointer ml-1'
              onClick={() => setIsShowFullDescription(!isShowFullDescription)}
            >
              {isShowFullDescription ? 'See less' : 'See more'}
            </span>
          )}
        </p>
      </div>

      {/* Footer with author and likes */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Avatar className='w-6 h-6'>
            <AvatarImage src={event.clubProfile?.img?.url ?? undefined} />
            <AvatarFallback className='text-sm'>
              {displayName.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <span className='text-sm text-gray-900 font-medium'>{displayName}</span>
        </div>

        <div className='flex items-center gap-1 text-sm text-gray-500'>
          <Heart className='w-6 h-6 text-primary' />
          <span className='text-primary'>{event.reactionCount || 0}</span>
        </div>
      </div>
    </div>
  );
};

export default EventTableItem;

const EventTableItemSkeleton = () => {
  return (
    <div className='p-4 sm:p-6 border-b'>
      {/* Header Skeleton */}
      <div className='flex items-start justify-between mb-2'>
        <Skeleton className='h-6 w-48' />
        <div className='flex items-center gap-2'>
          <Skeleton className='h-8 w-8 rounded' />
        </div>
      </div>

      {/* Date and time Skeleton */}
      <Skeleton className='h-4 w-64 mb-1' />

      {/* Location Skeleton */}
      <Skeleton className='h-4 w-32 mb-4' />

      {/* Description Skeleton */}
      <div className='mb-4 space-y-2'>
        <Skeleton className='h-4 w-full' />
        <Skeleton className='h-4 w-4/5' />
        <Skeleton className='h-4 w-3/5' />
      </div>

      {/* Footer Skeleton */}
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Skeleton className='w-6 h-6 rounded-full' />
          <Skeleton className='h-4 w-24' />
        </div>

        <div className='flex items-center gap-1'>
          <Skeleton className='h-4 w-4 rounded' />
          <Skeleton className='h-4 w-4' />
        </div>
      </div>
    </div>
  );
};
