import { useMemo, useCallback, useState } from 'react';
import { useClubRequestsQuery, ClubCategoryEnum } from '@/generated/graphql';
import usePagination from '@/hooks/usePagination';
import { useSearchQuery } from '@/hooks/useSearchQuery';
import SearchInput from '@/components/ui/search-input/SearchInput';
import { Button } from '@/components/ui/Button';
import { ListFilter, MoreVertical, Check, X, Eye } from 'lucide-react';
import { format } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { TablePagination } from '@/components/ui/table/TablePagination';
import { Checkbox } from '@/components/ui/Checkbox';
import TableData from '@/components/ui/table/TableData';
import { ColumnDef } from '@tanstack/react-table';

interface RequestsTabProps {
  clubId: string;
}

const RequestsTab = ({ clubId }: RequestsTabProps) => {
  const { pagination, setPagination } = usePagination();
  const { searchTemp, setSearchTemp } = useSearchQuery('club-requests');
  const [selectedRows, setSelectedRows] = useState<Record<string, boolean>>({});

  const { data: requestsData, loading: isLoadingRequests } = useClubRequestsQuery({
    variables: {
      paginationArgs: {
        page: pagination.pageIndex,
        limit: pagination.pageSize,
      },
      filter: {
        // search: searchTemp.trim() || undefined,
      },
    },
  });

  const requests = useMemo(() => requestsData?.clubRequests?.items ?? [], [requestsData]);
  const totalRequests = useMemo(() => requestsData?.clubRequests?.total ?? 0, [requestsData]);

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchTemp(value);
      setPagination({ pageIndex: 1, pageSize: 10 });
    },
    [setSearchTemp, setPagination]
  );

  const getCategoryLabel = (category: ClubCategoryEnum | null | undefined) => {
    switch (category) {
      case ClubCategoryEnum.Creative:
        return 'Creative';
      case ClubCategoryEnum.FitnessOutdoor:
        return 'Fitness & Outdoor';
      case ClubCategoryEnum.FoodDrink:
        return 'Food & Drink';
      case ClubCategoryEnum.Hobbies:
        return 'Hobbies';
      case ClubCategoryEnum.SocialFamily:
        return 'Social & Family';
      default:
        return 'N/A';
    }
  };

  const getStatusBadge = (status: string | null | undefined) => {
    switch (status) {
      case 'PENDING':
        return (
          <Badge variant='secondary' className='bg-yellow-100 text-yellow-800'>
            Pending
          </Badge>
        );
      case 'APPROVED':
        return (
          <Badge variant='secondary' className='bg-green-100 text-green-800'>
            Approved
          </Badge>
        );
      case 'DECLINED':
        return (
          <Badge variant='secondary' className='bg-red-100 text-red-800'>
            Declined
          </Badge>
        );
      default:
        return <Badge variant='secondary'>Unknown</Badge>;
    }
  };

  const getUserName = (firstName?: string | null, lastName?: string | null) => {
    if (firstName && lastName) return `${firstName} ${lastName}`;
    if (firstName) return firstName;
    if (lastName) return lastName;
    return 'Unknown User';
  };

  const columns: ColumnDef<any>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
    },
    {
      accessorKey: 'clubName',
      header: 'Club Name',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <div className='flex flex-col'>
            <span className='font-medium text-gray-900'>{request.clubName || 'Unnamed Club'}</span>
          </div>
        );
      },
      minSize: 200,
    },
    {
      accessorKey: 'category',
      header: 'Club Category',
      cell: ({ row }) => {
        const request = row.original;
        return <span className='text-gray-600'>{getCategoryLabel(request.category)}</span>;
      },
      minSize: 150,
    },
    {
      accessorKey: 'clubDescription',
      header: 'Description',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <span className='text-gray-600 max-w-[200px] truncate'>
            {request.clubDescription || '-'}
          </span>
        );
      },
      minSize: 200,
    },
    {
      accessorKey: 'user',
      header: 'User',
      cell: ({ row }) => {
        const request = row.original;
        const userName = getUserName(request.firstName, request.lastName);
        return (
          <div className='flex items-center gap-3'>
            <Avatar className='w-8 h-8'>
              <AvatarImage src={request.clubProfile?.img?.url || undefined} />
              <AvatarFallback className='text-xs'>
                {userName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className='text-gray-900'>{userName}</span>
          </div>
        );
      },
      minSize: 150,
    },
    {
      accessorKey: 'createdAt',
      header: 'Requested',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <span className='text-gray-600'>
            {format(new Date(request.createdAt || ''), 'MM/dd/yyyy')}
          </span>
        );
      },
      minSize: 120,
    },
    {
      accessorKey: 'actions',
      header: '',
      cell: ({ row }) => {
        const request = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                <MoreVertical className='w-4 h-4' />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem>
                <Eye className='w-4 h-4 mr-2' />
                View Details
              </DropdownMenuItem>
              {request.status === 'PENDING' && (
                <>
                  <DropdownMenuItem className='text-green-600'>
                    <Check className='w-4 h-4 mr-2' />
                    Approve Request
                  </DropdownMenuItem>
                  <DropdownMenuItem className='text-red-600'>
                    <X className='w-4 h-4 mr-2' />
                    Decline Request
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      minSize: 80,
      maxSize: 80,
    },
  ];

  return (
    <div className='w-full bg-white rounded-lg'>
      {/* Search and Filters */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex flex-col sm:flex-row gap-4 items-center justify-between'>
          <div className='flex-1 max-w-md'>
            <SearchInput name='search' onChange={handleSearchChange} value={searchTemp} />
          </div>
          <Button variant='outline' className='gap-2'>
            <ListFilter className='w-4 h-4' />
            Filters
          </Button>
        </div>
      </div>

      {/* Requests Header */}
      <div className='p-6 border-b border-gray-200'>
        <div className='flex items-center gap-3'>
          <h3 className='text-lg font-semibold text-gray-900'>Requests</h3>
          <Badge variant='secondary' className='bg-blue-100 text-blue-800'>
            {totalRequests}
          </Badge>
        </div>
      </div>

      {/* Requests Table */}
      <div className='w-full border rounded-b-lg overflow-auto'>
        <TableData
          columns={columns}
          data={requests}
          pagination={pagination}
          sorting={[]}
          filters={[]}
          onColumnFiltersChange={() => {}}
          onPaginationChange={(newPagination) => setPagination(newPagination)}
          onSortingChange={() => {}}
          onRowSelectionChange={setSelectedRows}
          initialRowSelected={selectedRows}
          getRowId={(row) => row.id}
        />
        <TablePagination
          pageCount={Math.ceil(totalRequests / pagination.pageSize)}
          currentPage={pagination.pageIndex - 1}
          onPageChange={(page) => setPagination({ ...pagination, pageIndex: page + 1 })}
        />
      </div>
    </div>
  );
};

export default RequestsTab;
