import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/Avatar';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropDownMenu';
import { Skeleton } from '@/components/ui/Skeleton';
import { AdminClubPost } from '@/generated/graphql';
import { Flag, Heart, MoreHorizontal } from 'lucide-react';
import { formatTimeAgo, getDisplayName } from '../../utils';
import { cn } from '@/lib/utils';
import { useMemo, useState } from 'react';

interface PostTableItemProps {
  post: AdminClubPost;
  isLoading?: boolean;
  showActions: boolean;
  onRemovePost: (postId: string) => void;
  onDisableClubAccess: (userId: string) => void;
  onUnflagPost: (postId: string) => void;
}

const PostTableItem = ({
  post,
  isLoading = false,
  showActions,
  onRemovePost,
  onDisableClubAccess,
  onUnflagPost,
}: PostTableItemProps) => {
  const [isSeeMore, setIsSeeMore] = useState(false);

  const postContent = useMemo(() => {
    if (isSeeMore) {
      return post.content;
    }
    return post.content?.slice(0, 200);
  }, [isSeeMore, post.content]);

  const hasReports = post.reports && post.reports.length > 0;
  const displayName = getDisplayName(
    post.clubProfile?.user?.firstName,
    post.clubProfile?.user?.lastName
  );

  if (isLoading) {
    return <PostTableItemSkeleton />;
  }

  return (
    <div
      key={post.id}
      className={cn('p-4 sm:p-6 border-b', hasReports ? 'bg-[#FCEFED80]' : 'border')}
    >
      <div className='flex justify-between gap-4'>
        <div className='flex flex-col gap-4'>
          <div className='flex gap-2'>
            {/* Avatar */}
            <Avatar className='w-10 h-10'>
              <AvatarImage src={post.clubProfile?.img?.url ?? undefined} />
              <AvatarFallback className='text-sm'>
                {displayName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>

            {/* Header */}
            <div className='flex flex-col sm:flex-row items-start gap-4'>
              <div className='flex flex-col'>
                <span className='font-medium text-sm text-gray-900'>{displayName}</span>
                {post.clubProfile?.user?.role && (
                  <p className='text-xs text-muted-foreground'>
                    {post.clubProfile?.user?.role === 'ADMIN' ? 'Admin' : 'User'}
                  </p>
                )}
              </div>
              {hasReports && (
                <div className='flex items-center gap-1'>
                  <Badge className='bg-[#FCEFED] gap-2 text-sm text-destructive-foreground hover:bg-[#FCEFED] font-medium'>
                    <Flag className='w-4 h-4 flex-shrink-0 text-destructive-foreground' />
                    {post.reports?.[0]?.category?.title || 'Flagged'}
                  </Badge>
                </div>
              )}
            </div>
          </div>

          {/* Post Content */}
          <div className='mb-3 mt-4'>
            <p className='text-sm'>
              {postContent}
              {post.content && post.content.length > 200 && (
                <>
                  {isSeeMore && <span>...</span>}
                  <span
                    className='text-primary cursor-pointer ml-1'
                    onClick={() => setIsSeeMore(!isSeeMore)}
                  >
                    {isSeeMore ? 'see less' : 'see more'}
                  </span>
                </>
              )}
            </p>
          </div>

          {/* time */}
          <span className='text-[13px]'>{formatTimeAgo(post.createdAt || '')}</span>

          {/* Report Reason (if flagged) */}
          {hasReports && post.reports?.[0]?.details && (
            <p className='text-sm text-destructive-foreground'>
              Report reason - {post.reports[0].details}
            </p>
          )}
        </div>

        {/* Content */}
        <div
          className={cn(
            'flex flex-col justify-between items-center min-h-[120px]',
            !showActions && 'justify-end items-end'
          )}
        >
          {/* Actions */}
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
                  <MoreHorizontal className='w-6 h-6' />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                {hasReports && (
                  <>
                    <DropdownMenuItem
                      onClick={() => {
                        if (post.clubProfile?.user?.id) {
                          onDisableClubAccess(post.clubProfile.user.id);
                        }
                      }}
                    >
                      Disable Club Access
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onUnflagPost(post.id)}>
                      Unflag Post
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem onClick={() => onRemovePost(post.id)}>
                  Delete Post
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <div className='flex items-center gap-4 text-sm text-gray-500'>
            <div className='flex items-center gap-1'>
              <Heart className='w-6 h-6 text-primary' />
              <span className='text-primary'>{post.reactionCount || 0}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PostTableItem;

const PostTableItemSkeleton = () => {
  return (
    <div className='p-4 sm:p-6'>
      <div className='flex flex-col sm:flex-row items-start gap-4'>
        {/* Avatar Skeleton */}
        <Skeleton className='w-10 h-10 rounded-full flex-shrink-0' />

        {/* Content Skeleton */}
        <div className='flex-1 min-w-0'>
          {/* Header Skeleton */}
          <div className='flex items-center gap-2 mb-2'>
            <Skeleton className='h-4 w-24' />
            <Skeleton className='h-5 w-12 rounded-full' />
          </div>

          {/* Post Content Skeleton */}
          <div className='mb-3 space-y-2'>
            <Skeleton className='h-4 w-full' />
            <Skeleton className='h-4 w-4/5' />
            <Skeleton className='h-4 w-3/5' />
          </div>

          {/* Footer Skeleton */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-4'>
              <Skeleton className='h-3 w-16' />
              <div className='flex items-center gap-1'>
                <Skeleton className='h-4 w-4 rounded' />
                <Skeleton className='h-3 w-4' />
              </div>
            </div>

            {/* Actions Skeleton */}
            <Skeleton className='h-8 w-8 rounded' />
          </div>
        </div>
      </div>
    </div>
  );
};
