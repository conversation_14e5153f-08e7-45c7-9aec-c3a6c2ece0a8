import { useSearchQuery } from '@/hooks/useSearchQuery';
import { useState } from 'react';

import { DateRange } from 'react-day-picker';

export interface PostFilters {
  // Applied filter states
  selectedUserId: string | null;
  selectedUserName: string | null;
  dateRange: DateRange | undefined;
  showFlaggedOnly: boolean;
  search: string;

  // Temporary filter states
  tempSelectedUserId: string | null;
  tempSelectedUserName: string | null;
  tempDateRange: DateRange | undefined;
  tempShowFlaggedOnly: boolean;
  tempUserSearch: string;
  isOpenFilters: boolean;

  // Actions
  setSelectedUserId: (userId: string | null) => void;
  setSelectedUserName: (userName: string | null) => void;
  setDateRange: (dateRange: DateRange | undefined) => void;
  setShowFlaggedOnly: (showFlagged: boolean) => void;
  setSearch: (search: string) => void;
  setTempSelectedUserId: (userId: string | null) => void;
  setTempSelectedUserName: (userName: string | null) => void;
  setTempDateRange: (dateRange: DateRange | undefined) => void;
  setTempShowFlaggedOnly: (showFlagged: boolean) => void;
  setTempUserSearch: (search: string) => void;
  handleApplyFilters: () => void;
  handleCancelFilters: () => void;
  onClearAll: () => void;
  handleFilterOpenChange: (open: boolean) => void;
}

export function usePostFilters(): PostFilters {
  // Applied filter states
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedUserName, setSelectedUserName] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [showFlaggedOnly, setShowFlaggedOnly] = useState<boolean>(false);
  const [search, setSearch] = useState<string>('');

  // Filter popover state
  const [isOpenFilters, setIsOpenFilters] = useState(false);

  // Temporary filter states (used while the filter modal is open)
  const [tempSelectedUserId, setTempSelectedUserId] = useState<string | null>(selectedUserId);
  const [tempSelectedUserName, setTempSelectedUserName] = useState<string | null>(selectedUserName);
  const [tempDateRange, setTempDateRange] = useState<DateRange | undefined>(dateRange);
  const [tempShowFlaggedOnly, setTempShowFlaggedOnly] = useState<boolean>(showFlaggedOnly);
  const [tempUserSearch, setTempUserSearch] = useState('');

  // Filter management functions
  const handleApplyFilters = () => {
    // Apply temporary filters to actual filter state
    setSelectedUserId(tempSelectedUserId);
    setSelectedUserName(tempSelectedUserName);
    setDateRange(tempDateRange);
    setShowFlaggedOnly(tempShowFlaggedOnly);

    setIsOpenFilters(false);
  };

  const handleCancelFilters = () => {
    // Close modal and reset temporary states to current applied filters
    setIsOpenFilters(false);
    setTempSelectedUserId(selectedUserId);
    setTempSelectedUserName(selectedUserName);
    setTempDateRange(dateRange);
    setTempShowFlaggedOnly(showFlaggedOnly);
    setTempUserSearch('');
  };

  const onClearAll = () => {
    // Reset all filters to default values
    const defaultUserId = null;
    const defaultUserName = null;
    const defaultDateRange = undefined;
    const defaultShowFlagged = false;

    setSelectedUserId(defaultUserId);
    setSelectedUserName(defaultUserName);
    setDateRange(defaultDateRange);
    setShowFlaggedOnly(defaultShowFlagged);
    setTempSelectedUserId(defaultUserId);
    setTempSelectedUserName(defaultUserName);
    setTempDateRange(defaultDateRange);
    setTempShowFlaggedOnly(defaultShowFlagged);
    setTempUserSearch('');

    setIsOpenFilters(false);
  };

  const handleFilterOpenChange = (open: boolean) => {
    setIsOpenFilters(open);

    // When closing without applying, reset temporary states
    if (!open) {
      setTempSelectedUserId(selectedUserId);
      setTempSelectedUserName(selectedUserName);
      setTempDateRange(dateRange);
      setTempShowFlaggedOnly(showFlaggedOnly);
      setTempUserSearch('');
    }
  };

  return {
    // Applied filter states
    selectedUserId,
    selectedUserName,
    dateRange,
    showFlaggedOnly,
    search,

    // Temporary filter states
    tempSelectedUserId,
    tempSelectedUserName,
    tempDateRange,
    tempShowFlaggedOnly,
    tempUserSearch,
    isOpenFilters,

    // Actions
    setSelectedUserId,
    setSelectedUserName,
    setDateRange,
    setShowFlaggedOnly,
    setSearch,
    setTempSelectedUserId,
    setTempSelectedUserName,
    setTempDateRange,
    setTempShowFlaggedOnly,
    setTempUserSearch,
    handleApplyFilters,
    handleCancelFilters,
    onClearAll,
    handleFilterOpenChange,
  };
}
