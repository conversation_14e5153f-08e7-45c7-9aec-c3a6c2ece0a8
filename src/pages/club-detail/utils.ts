import { ClubCategoryEnum } from '@/generated/graphql';

export const getDisplayName = (firstName?: string | null, lastName?: string | null) => {
  if (firstName && lastName) return `${firstName} ${lastName}`;
  if (firstName) return firstName;
  if (lastName) return lastName;
  return '';
};

export const formatTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

  if (diffInMinutes < 60) {
    return `${diffInMinutes}m`;
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)}h`;
  } else {
    return `${Math.floor(diffInMinutes / 1440)}d`;
  }
};

export const formatEventDateTime = (startTime?: string, endTime?: string) => {
  if (!startTime) return '';

  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : null;

  const dateStr = start.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });

  const startTimeStr = start.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  const endTimeStr = end
    ? end.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      })
    : null;

  return `${dateStr} • ${startTimeStr}${endTimeStr ? ` - ${endTimeStr}` : ''}`;
};

export const getClubCategoryLabel = (category: ClubCategoryEnum) => {
  switch (category) {
    case ClubCategoryEnum.Creative:
      return 'Creative';
    case ClubCategoryEnum.FitnessOutdoor:
      return 'Fitness & Outdoor';
    case ClubCategoryEnum.FoodDrink:
      return 'Food & Drink';
    case ClubCategoryEnum.Hobbies:
      return 'Hobbies';
    case ClubCategoryEnum.SocialFamily:
      return 'Social & Family';
    default:
      return '';
  }
};
