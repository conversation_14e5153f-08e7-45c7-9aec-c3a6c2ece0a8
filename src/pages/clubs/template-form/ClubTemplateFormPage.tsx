import { Button } from '@/components/ui/Button';
import { ClubFormData } from '@/pages/clubs/components/club-tab/CreateClubForm';
import {
  useClubTemplateByIdQuery,
  ClubRequestsDocument,
  useApprovedClubRequestCreationMutation,
} from '@/generated/graphql';
import { useClubActions } from '@/pages/clubs/hooks/useClubActions';
import { useImageUpload } from '@/pages/clubs/hooks/useImageUpload';

import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useMemo, useState } from 'react';
import CreateClubForm from '@/pages/clubs/components/club-tab/CreateClubForm';
import { toast } from '@/hooks/useToast';
import { TOAST_DURATION } from '@/lib/constants';

export function ClubTemplateFormPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id);

  const [isLoading, setIsLoading] = useState(false);
  const [approvedClubRequestCreation] = useApprovedClubRequestCreationMutation();

  const clubActions = useClubActions();
  const { uploadImage } = useImageUpload();

  // Check if we're coming from a club request approval
  const locationState = location.state as {
    fromClubRequest?: boolean;
    clubRequestData?: any;
  } | null;
  const isFromClubRequest = locationState?.fromClubRequest;
  const clubRequestData = locationState?.clubRequestData;

  // Helper function to approve club request after successful club creation
  const approveClubRequest = async (clubTemplateId: string) => {
    try {
      await approvedClubRequestCreation({
        variables: {
          clubRequestId: clubRequestData.requestId as string,
          clubTemplateId: clubTemplateId,
        },
        refetchQueries: [ClubRequestsDocument],
      });
    } catch (_) {
      toast({
        variant: 'destructive',
        title: 'Failed to approve club request.',
        duration: TOAST_DURATION,
      });
    }
  };

  const { data, loading: isLoadingClub } = useClubTemplateByIdQuery({
    variables: { clubTemplateByIdId: id as string },
    skip: !isEdit,
  });

  const [isFormValid, setIsFormValid] = useState(false);
  const [isFormDirty, setIsFormDirty] = useState(false);

  // Build initial values from fetched club template or club request data
  const initialValues = useMemo(() => {
    // Priority 1: If coming from club request, use that data
    if (isFromClubRequest && clubRequestData) {
      return {
        name: clubRequestData.clubName || '',
        description: clubRequestData.clubDescription || '',
        category: clubRequestData.category || undefined,
        about: clubRequestData.clubAbout || '',
      } as const;
    }

    // Priority 2: If editing existing club template, use that data
    if (isEdit) {
      const club = data?.clubTemplateById;
      if (!club) return {};
      return {
        name: club.name,
        description: club.description || '',
        category: club.category || undefined,
        about: club.about || '',
        img: club.img ? { url: club.img.url || '' } : undefined,
      } as const;
    }

    // Priority 3: Default empty values for new club
    return {};
  }, [isEdit, isFromClubRequest, clubRequestData, data?.clubTemplateById]);

  async function handleSubmit(values: ClubFormData) {
    try {
      setIsLoading(true);
      let uploadedImage = null as ClubFormData['uploadedImage'] | null;

      if (values.image) {
        uploadedImage = await uploadImage(values.image);
        if (!uploadedImage) return;
      }

      if (isEdit && id) {
        const updateData = {
          id,
          name: values.name,
          description: values.description,
          category: values.category,
          ...(values.about !== undefined && { about: values.about }),
          ...(uploadedImage?.id && { imgId: uploadedImage.id }),
        };
        await clubActions.handleConfirmUpdateClub(updateData);
      } else {
        // Create the club
        // If this was from a club request approval, approve the request after successful creation
        if (isFromClubRequest && clubRequestData?.requestId) {
          await clubActions.handleConfirmCreateClub(
            { ...values, imgId: uploadedImage?.id },
            (clubTemplateId) => approveClubRequest(clubTemplateId)
          );
        } else {
          await clubActions.handleConfirmCreateClub({ ...values, imgId: uploadedImage?.id });
        }
      }
    } catch (_error) {
      // Error toasts handled in hooks for club creation
    } finally {
      setIsLoading(false);
    }
  }

  function handleCancel() {
    navigate(-1);
  }

  const title = isEdit ? 'Edit club' : 'New club';
  const subtitle =
    isEdit || isFromClubRequest ? 'Update club details here.' : 'Add club details here.';
  const submitLabel = isEdit ? 'Save' : 'Create Club';

  return (
    <div className='w-full flex py-4 pt-8 space-y-4 flex-col flex-1 px-4 sm:px-12 '>
      <div className='flex items-start pb-6 w-full  justify-between border-b border-gray-200'>
        <div>
          <h1 className='text-lg text-gray-900 font-semibold'>{title}</h1>
          <p className='text-sm mt-1'>{subtitle}</p>
        </div>
        <div className='flex space-x-3'>
          <Button variant='outline' onClick={handleCancel} className='text-sm'>
            Cancel
          </Button>
          <Button
            loading={isLoading}
            disabled={isLoading || !isFormValid || !isFormDirty}
            form='club-form'
            className='text-sm'
            type='submit'
          >
            {submitLabel}
          </Button>
        </div>
      </div>

      <div className='mt-6'>
        <CreateClubForm
          onCancel={handleCancel}
          onConfirm={handleSubmit}
          onFormChange={(_, isValid, isDirty) => {
            setIsFormValid(isValid);
            setIsFormDirty(isDirty);
          }}
          initialValues={initialValues}
          isEdit={isEdit}
          isLoading={isLoadingClub || isLoading}
          hideButtons={true}
        />
      </div>
    </div>
  );
}

export default ClubTemplateFormPage;
