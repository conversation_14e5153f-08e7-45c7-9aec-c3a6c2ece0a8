import { Checkbox } from '@/components/ui/Checkbox';
import ActionCell from '@/components/ui/table/ActionCell';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { ClubTemplate, ClubCategoryEnum } from '@/generated/graphql';
import { DATE_FORMAT_MM_DD_YYYY } from '@/lib/constants';
import { ColumnDef } from '@tanstack/react-table';
import { format } from 'date-fns';

interface ClubColumnsProps {
  isLoading: boolean;
  showActions: boolean;
  selectedRowId: string | null;
  actionCellRef?: React.RefObject<HTMLDivElement>;
  onEdit: (club: ClubTemplate) => void;
  onDelete: (club: ClubTemplate) => void;
  onNavigate: (clubId: string) => void;
  setSelectedRowId: (id: string | null) => void;
}

export function generateClubColumns({
  isLoading,
  selectedRowId,
  showActions,
  onEdit,
  onDelete,
  actionCellRef,
  onNavigate,
  setSelectedRowId,
}: ClubColumnsProps) {
  const columns: ColumnDef<ClubTemplate>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'name',
      id: 'name', // Explicit ID for sorting
      header: ({ column }) => <HeaderColumn column={column}>Club Name</HeaderColumn>,
      cell: ({ row }) => {
        const club = row.original;
        const clubName = club?.name || 'Unnamed Club';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>{clubName}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'category',
      header: ({ column }) => <HeaderColumn column={column}>Club Category</HeaderColumn>,
      cell: ({ row }) => {
        const category = row.original.category;
        const getCategoryLabel = (category: ClubCategoryEnum | null | undefined) => {
          switch (category) {
            case ClubCategoryEnum.Creative:
              return 'Creative';
            case ClubCategoryEnum.FitnessOutdoor:
              return 'Fitness & Outdoor';
            case ClubCategoryEnum.FoodDrink:
              return 'Food & Drink';
            case ClubCategoryEnum.Hobbies:
              return 'Hobbies';
            case ClubCategoryEnum.SocialFamily:
              return 'Social & Family';
            default:
              return 'N/A';
          }
        };

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>
                {getCategoryLabel(category)}
              </span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 150,
      size: 180,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'description',
      header: ({ column }) => <HeaderColumn column={column}>Description</HeaderColumn>,
      cell: ({ row }) => {
        const description = row.original.description;
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-foreground font-normal'>{description}</span>
            </div>
          </SkeletonCell>
        );
      },
      enableSorting: false,
      minSize: 200,
      size: 300,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      accessorKey: 'lastUpdated',
      id: 'lastUpdated', // Explicit ID for sorting
      header: ({ column }) => <HeaderColumn column={column}>Last Updated</HeaderColumn>,

      cell: ({ row }) => {
        const lastUpdated = row.original.updatedAt
          ? format(new Date(row.original.updatedAt), DATE_FORMAT_MM_DD_YYYY)
          : '';
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>{lastUpdated}</span>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    ...(showActions
      ? [
          {
            accessorKey: 'actions',
            header: '',
            cell: ({ row }) => {
              const club = row.original;
              const isSelected = selectedRowId === row.id;

              return (
                <SkeletonCell isLoading={isLoading} skeletonCount={1}>
                  <ActionCell
                    ref={actionCellRef}
                    isSelected={isSelected}
                    setSelectedRowId={setSelectedRowId}
                    actions={[
                      {
                        label: 'Edit Club Details',
                        onClick: () => onEdit(club),
                      },
                      {
                        label: 'Delete Club',
                        onClick: () => onDelete(club),
                      },
                    ]}
                  />
                </SkeletonCell>
              );
            },
            minSize: 50,
            size: 50,
            meta: {
              padding: '20px 16px',
            },
          } as ColumnDef<ClubTemplate>,
        ]
      : []),
  ];

  return columns;
}
