import { useCallback, forwardRef } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../DropDownMenu';
import { Button } from '../Button';
import { DotsVerticalIcon } from '@radix-ui/react-icons';
import { cn } from '@/lib/utils';

interface Action {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

interface ActionCellProps {
  actions: Action[];
  isSelected: boolean;
  setSelectedRowId: (id: string | null) => void;
}

const ActionCell = forwardRef<HTMLDivElement, ActionCellProps>(
  ({ actions, isSelected, setSelectedRowId }, ref) => {
    const handleOpenChange = useCallback(
      (open: boolean) => {
        if (!open) {
          setSelectedRowId(null);
        }
      },
      [setSelectedRowId]
    );

    const isPluralAction = actions.length > 1;

    return (
      <div ref={ref} className='relative'>
        <DropdownMenu modal open={isSelected} onOpenChange={handleOpenChange}>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-5 w-5 p-0'>
              <span className='sr-only'>Open menu</span>
              <DotsVerticalIcon className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end' className='p-0 w-60'>
            {actions?.map((action, index) => (
              <DropdownMenuItem
                key={index}
                className={cn(
                  'rounded-none p-2 mt-2 text-gray-700 text-sm font-medium cursor-pointer',
                  {
                    'mt-0': !isPluralAction,
                  }
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  action.onClick();
                }}
                disabled={action.disabled}
              >
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }
);

ActionCell.displayName = 'ActionCell';

export default ActionCell;
