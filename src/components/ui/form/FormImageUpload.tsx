import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import ImageUpload from '@/components/ui/ImageUpload';
import { cn } from '@/lib/utils';

import { FormDescription, FormField, FormItem } from '@/components/ui/form/Form';
import { Label } from './Label';

interface FormImageUploadProps {
  name: string;
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  initialUrl?: string | null;
  disabled?: boolean;
  placeholder?: string;
  formItemClassName?: string;
  formLabelClassName?: string;
}

export function FormImageUpload({
  name,
  label,
  description,
  initialUrl = null,
  disabled,
  placeholder = 'Upload Image',
  formItemClassName,
  formLabelClassName,
}: FormImageUploadProps) {
  const { control, setError, clearErrors } = useFormContext();

  return (
    <FormField
      control={control}
      name={name as any}
      render={({ field, formState: { errors } }) => {
        return (
          <FormItem
            className={cn(
              'flex flex-col space-y-2 w-full',
              errors[name] && 'is-error group',
              formItemClassName
            )}
          >
            {label && (
              <Label className={cn('text-sm font-medium', formLabelClassName)}>{label}</Label>
            )}
            <div>
              <ImageUpload
                value={initialUrl}
                onChange={(file) => field.onChange(file)}
                disabled={disabled}
                placeholder={placeholder}
                hasError={!!errors[name]}
                errorMessage={errors[name]?.message as string}
                accept={'image/png,image/jpeg,image/svg+xml'}
                onInvalidFile={(message) => {
                  setError(name as any, { type: 'custom', message });
                }}
                onRemove={() => {
                  clearErrors(name as any);
                }}
              />
              {/* {errors[name] && (
                <p className='text-[13px] text-destructive mt-1'>
                  {errors[name]?.message
                    ? String(errors[name]?.message)
                    : 'This field is not valid'}
                </p>
              )} */}
            </div>
            {typeof description === 'string' ? (
              <FormDescription>{description}</FormDescription>
            ) : (
              description
            )}
          </FormItem>
        );
      }}
    />
  );
}

export default FormImageUpload;
