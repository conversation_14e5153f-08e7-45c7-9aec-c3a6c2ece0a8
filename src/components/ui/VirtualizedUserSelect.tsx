import React, { useState, useCallback, useMemo, useRef } from 'react';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import { useUsersQuery } from '@/generated/graphql';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Command, CommandInput } from '@/components/ui/Command';
import { ChevronDown, Check as CheckIcon, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VirtualizedUserSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface UserItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    users: User[];
    selectedValue: string;
    onSelect: (email: string) => void;
    isItemLoaded: (index: number) => boolean;
  };
}

// Virtual list item component
const UserItem: React.FC<UserItemProps> = ({ index, style, data }) => {
  const { users, selectedValue, onSelect, isItemLoaded } = data;

  // Handle "All Users" option
  if (index === 0) {
    return (
      <div style={style}>
        <div
          className='flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-100 text-sm'
          onClick={() => onSelect('All Users')}
        >
          <span>All Users</span>
          {selectedValue === 'All Users' && <CheckIcon className='h-4 w-4 text-primary' />}
        </div>
      </div>
    );
  }

  const userIndex = index - 1; // Adjust for "All Users" option
  const user = users[userIndex];

  // Loading placeholder
  if (!isItemLoaded(index) || !user) {
    return (
      <div style={style}>
        <div className='flex items-center px-2 py-2 text-sm text-gray-500'>Loading...</div>
      </div>
    );
  }

  return (
    <div style={style}>
      <div
        className='flex items-center justify-between px-2 py-2 cursor-pointer hover:bg-gray-100 text-sm'
        onClick={() => onSelect(user.email)}
      >
        <div className='flex flex-col'>
          <span>
            {user.firstName} {user.lastName}
          </span>
          <span className='text-xs text-gray-500'>{user.email}</span>
        </div>
        {selectedValue === user.email && <CheckIcon className='h-4 w-4 text-primary' />}
      </div>
    </div>
  );
};

const VirtualizedUserSelect: React.FC<VirtualizedUserSelectProps> = ({
  value,
  onValueChange,
  placeholder = 'Select user...',
  className,
  disabled = false,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const isLoadingMoreRef = useRef(false);

  const {
    data: usersData,
    loading,
    fetchMore,
  } = useUsersQuery({
    variables: {
      paginationArgs: {
        page: 1,
        limit: 50, // Increased for better virtual performance
      },
      search: searchTerm.trim() || undefined,
    },
    notifyOnNetworkStatusChange: true,
  });

  const users = useMemo(() => {
    return usersData?.users?.items || [];
  }, [usersData]);

  const totalUsers = usersData?.users?.total || 0;
  const currentPage = usersData?.users?.page || 1;

  // Check if we have more data to load
  const hasMoreData = users.length < totalUsers;

  // Total items = 1 (All Users) + users.length + buffer for loading
  const itemCount = hasMoreData ? users.length + 2 : users.length + 1; // +1 for "All Users", +1 for loading buffer

  // Check if item is loaded
  const isItemLoaded = useCallback(
    (index: number) => {
      if (index === 0) return true; // "All Users" is always loaded
      return !!users[index - 1]; // Adjust for "All Users" offset
    },
    [users]
  );

  // Load more items for infinite scroll
  const loadMoreItems = useCallback(
    async (startIndex: number, stopIndex: number) => {
      if (!hasMoreData || loading || isLoadingMoreRef.current) {
        return;
      }

      isLoadingMoreRef.current = true;
      const nextPage = currentPage + 1;

      try {
        await fetchMore({
          variables: {
            paginationArgs: {
              page: nextPage,
              limit: 50,
            },
            search: searchTerm.trim() || undefined,
          },
          updateQuery: (prev, { fetchMoreResult }) => {
            if (!fetchMoreResult) return prev;

            return {
              users: {
                ...fetchMoreResult.users,
                items: [...prev.users.items, ...fetchMoreResult.users.items],
              },
            };
          },
        });
      } catch (error) {
        console.error('Error loading more users:', error);
      } finally {
        isLoadingMoreRef.current = false;
      }
    },
    [fetchMore, hasMoreData, loading, currentPage, searchTerm]
  );

  const handleSearchChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  const handleSelectUser = useCallback(
    (userId: string) => {
      onValueChange(userId);
      setIsOpen(false);
    },
    [onValueChange]
  );

  const getSelectedUserName = useCallback(() => {
    if (value === 'All Users') return placeholder; // Show placeholder instead of "All Users"
    if (!value) return placeholder;
    const selectedUser = users.find((user) => user.email === value);
    return selectedUser ? `${selectedUser.firstName} ${selectedUser.lastName}` : placeholder;
  }, [value, users, placeholder]);

  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setSearchTerm('');
      isLoadingMoreRef.current = false;
    }
  }, []);

  const handleClearSelection = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onValueChange('All Users');
    },
    [onValueChange]
  );

  // Data for virtual list
  const listData = useMemo(
    () => ({
      users,
      selectedValue: value,
      onSelect: handleSelectUser,
      isItemLoaded,
    }),
    [users, value, handleSelectUser, isItemLoaded]
  );

  return (
    <Popover modal={false} open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          disabled={disabled}
          className={cn(
            'w-full justify-between text-[#191E3B] font-normal p-3 border border-gray-200 rounded-md text-sm bg-white',
            className
          )}
        >
          <span className='truncate'>{getSelectedUserName()}</span>
          <div className='flex items-center gap-1 ml-2 shrink-0'>
            {value && value !== 'All Users' && (
              <X className='h-4 w-4 opacity-50 hover:opacity-100' onClick={handleClearSelection} />
            )}
            <ChevronDown className='h-4 w-4 opacity-50' />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(e) => e.preventDefault()}
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        <Command>
          <CommandInput
            placeholder='Search users...'
            value={searchTerm}
            onValueChange={handleSearchChange}
          />

          {/* Show loading message only when API is loading and no data */}
          {loading && users.length === 0 && (
            <div className='p-4 text-center text-sm text-gray-500'>Loading users...</div>
          )}

          {/* Virtual List Container */}
          {(!loading || users.length > 0) && (
            <div className='border-t'>
              <InfiniteLoader
                isItemLoaded={isItemLoaded}
                itemCount={itemCount}
                loadMoreItems={loadMoreItems}
                threshold={5} // Load more when 5 items from bottom
              >
                {({ onItemsRendered, ref }: any) => (
                  <List
                    ref={ref}
                    width='100%'
                    height={256} // max-h-64 equivalent (16 * 16px)
                    itemCount={itemCount}
                    itemSize={56} // ~14 chars per line * 4 lines = 56px
                    itemData={listData}
                    onItemsRendered={onItemsRendered}
                    overscanCount={5} // Render 5 extra items for smooth scrolling
                  >
                    {UserItem}
                  </List>
                )}
              </InfiniteLoader>

              {/* Debug info */}
              <div className='p-2 text-xs text-gray-400 border-t'>
                Showing {users.length} of {totalUsers} users
                {hasMoreData && ` (${totalUsers - users.length} more available)`}
              </div>
            </div>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default VirtualizedUserSelect;
